<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多选题验证逻辑测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .test-case {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .test-case h3 {
            margin-top: 0;
            color: #333;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .option-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .option-group input[type="checkbox"] {
            margin: 0;
        }

        .result {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-weight: 500;
        }

        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: #007bff;
            color: white;
            cursor: pointer;
            border-radius: 4px;
            margin: 5px;
        }

        .btn:hover {
            background: #0056b3;
        }

        .explanation {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 多选题验证逻辑测试</h1>
        
        <div class="explanation">
            <h3>验证规则说明：</h3>
            <ul>
                <li><strong>多选题</strong>：可以选择1-3个选项作为正确答案</li>
                <li><strong>不允许</strong>：选择全部4个选项（这样就没有错误选项了）</li>
                <li><strong>不允许</strong>：不选择任何选项</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>交互式测试</h2>
            <div class="test-case">
                <h3>多选题示例</h3>
                <p><strong>题目：</strong>以下哪些是编程语言？</p>
                
                <div class="options-container">
                    <div class="option-group">
                        <input type="checkbox" id="answerA" name="correctAnswers" value="A" onchange="validateAnswerSelection()">
                        <label for="answerA">A: JavaScript</label>
                    </div>
                    <div class="option-group">
                        <input type="checkbox" id="answerB" name="correctAnswers" value="B" onchange="validateAnswerSelection()">
                        <label for="answerB">B: Python</label>
                    </div>
                    <div class="option-group">
                        <input type="checkbox" id="answerC" name="correctAnswers" value="C" onchange="validateAnswerSelection()">
                        <label for="answerC">C: HTML</label>
                    </div>
                    <div class="option-group">
                        <input type="checkbox" id="answerD" name="correctAnswers" value="D" onchange="validateAnswerSelection()">
                        <label for="answerD">D: Java</label>
                    </div>
                </div>
                
                <div id="validationResult" class="result" style="display: none;"></div>
                
                <button class="btn" onclick="testSave()">测试保存</button>
                <button class="btn" onclick="resetSelection()">重置选择</button>
            </div>
        </div>

        <div class="test-section">
            <h2>自动化测试用例</h2>
            <div id="autoTestResults"></div>
            <button class="btn" onclick="runAutoTests()">运行所有测试</button>
        </div>
    </div>

    <script>
        // 验证答案选择（复制自主文件的逻辑）
        function validateAnswerSelection() {
            const selectedAnswers = [];
            const optionKeys = ['A', 'B', 'C', 'D'];

            optionKeys.forEach(key => {
                const answerInput = document.getElementById(`answer${key}`);
                if (answerInput && answerInput.checked) {
                    selectedAnswers.push(key);
                }
            });

            // 移除之前的错误提示
            const existingError = document.getElementById('answerError');
            if (existingError) {
                existingError.remove();
            }

            const resultDiv = document.getElementById('validationResult');
            let errorMessage = '';

            // 多选题验证逻辑
            if (selectedAnswers.length >= 4) {
                errorMessage = '多选题不能选择全部选项';
            } else if (selectedAnswers.length === 0) {
                errorMessage = '请至少选择一个正确答案';
            }

            if (errorMessage) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ${errorMessage}`;
                resultDiv.style.display = 'block';
            } else {
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 已选择 ${selectedAnswers.length} 个选项: ${selectedAnswers.join(', ')} - 验证通过`;
                resultDiv.style.display = 'block';
            }

            return { valid: !errorMessage, selectedCount: selectedAnswers.length, selectedAnswers };
        }

        // 测试保存逻辑
        function testSave() {
            const validation = validateAnswerSelection();
            const resultDiv = document.getElementById('validationResult');
            
            if (validation.valid) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 保存成功！选择了 ${validation.selectedCount} 个正确答案`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 保存失败！请修正选择`;
            }
            resultDiv.style.display = 'block';
        }

        // 重置选择
        function resetSelection() {
            const optionKeys = ['A', 'B', 'C', 'D'];
            optionKeys.forEach(key => {
                const input = document.getElementById(`answer${key}`);
                if (input) {
                    input.checked = false;
                }
            });
            
            const resultDiv = document.getElementById('validationResult');
            resultDiv.style.display = 'none';
        }

        // 自动化测试
        function runAutoTests() {
            const testCases = [
                { name: '选择0个选项', selections: [], expected: false, reason: '必须至少选择一个' },
                { name: '选择1个选项', selections: ['A'], expected: true, reason: '单个选项有效' },
                { name: '选择2个选项', selections: ['A', 'B'], expected: true, reason: '两个选项有效' },
                { name: '选择3个选项', selections: ['A', 'B', 'C'], expected: true, reason: '三个选项有效' },
                { name: '选择4个选项', selections: ['A', 'B', 'C', 'D'], expected: false, reason: '不能选择全部选项' }
            ];

            const resultsContainer = document.getElementById('autoTestResults');
            resultsContainer.innerHTML = '';

            testCases.forEach((testCase, index) => {
                const result = simulateValidation(testCase.selections);
                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                
                const isPass = result.valid === testCase.expected;
                const statusIcon = isPass ? '✅' : '❌';
                const statusClass = isPass ? 'success' : 'error';
                
                testDiv.innerHTML = `
                    <h3>${statusIcon} 测试 ${index + 1}: ${testCase.name}</h3>
                    <p><strong>选择：</strong>${testCase.selections.length > 0 ? testCase.selections.join(', ') : '无'}</p>
                    <p><strong>期望结果：</strong>${testCase.expected ? '通过' : '失败'}</p>
                    <p><strong>实际结果：</strong>${result.valid ? '通过' : '失败'}</p>
                    <p><strong>原因：</strong>${testCase.reason}</p>
                    <div class="result ${statusClass}">
                        ${isPass ? '测试通过' : '测试失败'}
                    </div>
                `;
                
                resultsContainer.appendChild(testDiv);
            });
        }

        // 模拟验证逻辑
        function simulateValidation(selections) {
            const selectedCount = selections.length;
            
            if (selectedCount >= 4) {
                return { valid: false, message: '多选题不能选择全部选项' };
            } else if (selectedCount === 0) {
                return { valid: false, message: '请至少选择一个正确答案' };
            } else {
                return { valid: true, message: '验证通过' };
            }
        }

        // 页面加载时运行测试
        document.addEventListener('DOMContentLoaded', function() {
            runAutoTests();
        });
    </script>
</body>
</html>
