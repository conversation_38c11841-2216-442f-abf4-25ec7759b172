<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qwen-Solve API 管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }

        .logo {
            font-size: 4em;
            margin-bottom: 20px;
        }

        .title {
            font-size: 2.5em;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 40px;
        }

        .nav-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .nav-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px 20px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            border-color: #3498db;
        }

        .nav-card-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .nav-card-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .nav-card-desc {
            color: #7f8c8d;
            font-size: 0.9em;
            line-height: 1.5;
        }

        .features {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .features-title {
            font-size: 1.5em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            text-align: left;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #555;
        }

        .feature-icon {
            color: #27ae60;
            font-weight: bold;
        }

        .api-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #27ae60;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .status-offline {
            background-color: #e74c3c;
        }

        .status-text {
            font-weight: 500;
            color: #27ae60;
        }

        .status-text.offline {
            color: #e74c3c;
        }

        .footer {
            color: #95a5a6;
            font-size: 0.9em;
            margin-top: 30px;
        }

        .footer a {
            color: #3498db;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .container {
                padding: 40px 20px;
            }
            
            .title {
                font-size: 2em;
            }
            
            .nav-cards {
                grid-template-columns: 1fr;
            }
            
            .features-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🤖</div>
        <h1 class="title">Qwen-Solve API</h1>
        <p class="subtitle">智能题目识别与管理系统</p>
        
        <!-- API状态 -->
        <div class="api-status" id="apiStatus">
            <div class="status-indicator" id="statusIndicator"></div>
            <span class="status-text" id="statusText">检查API连接状态...</span>
        </div>
        
        <!-- 导航卡片 -->
        <div class="nav-cards">
            <a href="request-logs.html" class="nav-card">
                <div class="nav-card-icon">📊</div>
                <div class="nav-card-title">请求日志查询</div>
                <div class="nav-card-desc">
                    查看API请求记录、统计信息、错误日志等，
                    监控系统运行状态和性能指标
                </div>
            </a>
            
            <a href="question-bank.html" class="nav-card">
                <div class="nav-card-icon">📚</div>
                <div class="nav-card-title">题库维护管理</div>
                <div class="nav-card-desc">
                    管理题目数据，支持增删改查、
                    验证状态管理、批量操作等功能
                </div>
            </a>
        </div>
        
        <!-- 功能特性 -->
        <div class="features">
            <div class="features-title">🚀 系统特性</div>
            <div class="features-list">
                <div class="feature-item">
                    <span class="feature-icon">✅</span>
                    <span>实时日志监控</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">✅</span>
                    <span>题目智能管理</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">✅</span>
                    <span>数据统计分析</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">✅</span>
                    <span>响应式设计</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">✅</span>
                    <span>操作简单直观</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">✅</span>
                    <span>实时数据更新</span>
                </div>
            </div>
        </div>
        
        <!-- 页脚 -->
        <div class="footer">
            <p>基于 <a href="完整接入文档.md" target="_blank">Qwen-Solve API</a> 构建</p>
            <p>支持单选题、多选题、判断题的智能识别与管理</p>
        </div>
    </div>

    <script>
        // 检查API连接状态
        async function checkAPIStatus() {
            const statusIndicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            const apiStatus = document.getElementById('apiStatus');

            try {
                const response = await fetch('http://solve.igmdns.com/health', {
                    method: 'GET',
                    timeout: 5000
                });
                
                if (response.ok) {
                    statusIndicator.classList.remove('status-offline');
                    statusText.classList.remove('offline');
                    statusText.textContent = 'API服务运行正常';
                    apiStatus.style.backgroundColor = '#e8f5e8';
                } else {
                    throw new Error('API响应异常');
                }
            } catch (error) {
                statusIndicator.classList.add('status-offline');
                statusText.classList.add('offline');
                statusText.textContent = 'API服务连接失败';
                apiStatus.style.backgroundColor = '#ffeaea';
                console.error('API状态检查失败:', error);
            }
        }

        // 页面加载完成后检查API状态
        document.addEventListener('DOMContentLoaded', function() {
            checkAPIStatus();
            
            // 每30秒检查一次API状态
            setInterval(checkAPIStatus, 30000);
        });

        // 添加页面切换动画
        document.querySelectorAll('.nav-card').forEach(card => {
            card.addEventListener('click', function(e) {
                // 添加点击效果
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
