const express = require('express');
const path = require('path');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');

const app = express();

// 环境变量配置
const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || '0.0.0.0';
const API_BASE_URL = process.env.API_BASE_URL || 'http://solve.igmdns.com';

// 中间件配置
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:", "http:"],
            connectSrc: ["'self'", API_BASE_URL, "https:", "http:"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
}));

app.use(compression());
app.use(cors());

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// API配置注入中间件
app.use((req, res, next) => {
    // 为HTML文件注入API配置
    if (req.path.endsWith('.html')) {
        res.setHeader('Content-Type', 'text/html; charset=utf-8');
    }
    next();
});

// 路由配置
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/logs', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'request-logs.html'));
});

app.get('/questions', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'question-bank.html'));
});

// API配置端点
app.get('/api/config', (req, res) => {
    res.json({
        apiBaseUrl: API_BASE_URL,
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'production'
    });
});

// 健康检查端点
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '1.0.0'
    });
});

// 404处理
app.use((req, res) => {
    res.status(404).sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 错误处理
app.use((err, req, res, next) => {
    console.error('服务器错误:', err);
    res.status(500).json({
        error: '服务器内部错误',
        message: process.env.NODE_ENV === 'development' ? err.message : '请联系管理员'
    });
});

// 启动服务器
app.listen(PORT, HOST, () => {
    console.log(`🚀 Qwen-Solve 管理系统已启动`);
    console.log(`📍 服务地址: http://${HOST}:${PORT}`);
    console.log(`🔗 API地址: ${API_BASE_URL}`);
    console.log(`🌍 环境: ${process.env.NODE_ENV || 'production'}`);
    console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('收到SIGTERM信号，正在优雅关闭...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('收到SIGINT信号，正在优雅关闭...');
    process.exit(0);
});
