<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .demo-table th,
        .demo-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .demo-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .demo-table tr:hover {
            background-color: #f8f9fa;
        }

        .btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            margin-right: 5px;
            font-size: 12px;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
            border-color: #28a745;
        }

        .btn-warning {
            background-color: #ffc107;
            color: #212529;
            border-color: #ffc107;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            padding: 20px;
            background: white;
            margin-top: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            flex-wrap: wrap;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .pagination button:hover:not(:disabled) {
            background-color: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 页码锚点样式 */
        .page-numbers {
            display: flex;
            gap: 5px;
            align-items: center;
            flex-wrap: wrap;
        }

        .page-number {
            padding: 6px 10px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #333;
            font-size: 14px;
            min-width: 32px;
            text-align: center;
        }

        .page-number:hover {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .page-number.current {
            background: #3498db;
            color: white;
            border-color: #3498db;
            font-weight: bold;
        }

        .page-number.ellipsis {
            border: none;
            background: none;
            cursor: default;
            color: #999;
        }

        .page-number.ellipsis:hover {
            background: none;
            color: #999;
        }

        /* 页数跳转样式 */
        .page-jump {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
        }

        .page-jump input {
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
            width: 60px;
        }

        .page-jump button {
            padding: 4px 12px;
            font-size: 12px;
        }

        .demo-controls {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .demo-controls button {
            margin: 0 5px;
        }

        @media (max-width: 768px) {
            .pagination {
                flex-direction: column;
                gap: 10px;
                padding: 15px;
            }
            
            .page-numbers {
                justify-content: center;
                max-width: 100%;
                overflow-x: auto;
                padding: 5px 0;
            }
            
            .page-jump {
                justify-content: center;
            }
            
            .page-jump input {
                width: 50px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 分页功能演示</h1>
        
        <div class="demo-controls">
            <h3>测试不同的总页数：</h3>
            <button onclick="setTotalPages(5)">5页</button>
            <button onclick="setTotalPages(15)">15页</button>
            <button onclick="setTotalPages(50)">50页</button>
            <button onclick="setTotalPages(100)">100页</button>
        </div>

        <table class="demo-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>题目类型</th>
                    <th>题目内容</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="demoTableBody">
                <!-- 演示数据将通过JavaScript生成 -->
            </tbody>
        </table>

        <!-- 分页 -->
        <div class="pagination" id="pagination">
            <button id="prevPage" onclick="changePage(-1)">« 上一页</button>
            
            <!-- 页码锚点区域 -->
            <div class="page-numbers" id="pageNumbers">
                <!-- 页码将通过JavaScript动态生成 -->
            </div>
            
            <!-- 页数跳转 -->
            <div class="page-jump">
                <span>跳转到</span>
                <input type="number" id="jumpPageInput" min="1" placeholder="页码">
                <button onclick="jumpToPage()">跳转</button>
            </div>
            
            <button id="nextPage" onclick="changePage(1)">下一页 »</button>
            <span id="pageInfo">第 1 页，共 1 页</span>
        </div>
    </div>

    <script>
        // 演示数据
        let currentPage = 1;
        let totalPages = 15;
        let pageSize = 10;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateDemoData();
            updatePagination();
            
            // 页数跳转输入框回车事件
            const jumpInput = document.getElementById('jumpPageInput');
            jumpInput.addEventListener('keypress', function(event) {
                if (event.key === 'Enter') {
                    jumpToPage();
                }
            });
        });

        // 生成演示数据
        function generateDemoData() {
            const tbody = document.getElementById('demoTableBody');
            tbody.innerHTML = '';
            
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(startIndex + pageSize - 1, totalPages * pageSize);
            
            for (let i = startIndex; i <= endIndex; i++) {
                const row = document.createElement('tr');
                const isVerified = Math.random() > 0.5;
                const types = ['单选题', '多选题', '判断题'];
                const type = types[Math.floor(Math.random() * types.length)];
                
                row.innerHTML = `
                    <td>${i}</td>
                    <td>${type}</td>
                    <td>这是第${i}道题目的内容...</td>
                    <td>
                        <button class="btn ${isVerified ? 'btn-success' : 'btn-danger'}" 
                                onclick="toggleDemo(this, ${i})">
                            ${isVerified ? '已验证' : '未验证'}
                        </button>
                    </td>
                    <td>
                        <button class="btn btn-warning" onclick="editDemo(${i})">编辑</button>
                        <button class="btn btn-danger" onclick="deleteDemo(${i})">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            }
        }

        // 演示切换验证状态
        function toggleDemo(button, id) {
            const isVerified = button.textContent.trim() === '已验证';
            button.className = `btn ${!isVerified ? 'btn-success' : 'btn-danger'}`;
            button.textContent = !isVerified ? '已验证' : '未验证';
            
            // 添加更新动画效果
            const row = button.closest('tr');
            row.style.backgroundColor = '#e8f5e8';
            setTimeout(() => {
                row.style.backgroundColor = '';
            }, 2000);
        }

        // 演示编辑功能
        function editDemo(id) {
            alert(`编辑题目 ${id} - 在实际应用中，这里会打开编辑模态框`);
        }

        // 演示删除功能
        function deleteDemo(id) {
            if (confirm(`确定要删除题目 ${id} 吗？`)) {
                alert(`题目 ${id} 已删除`);
            }
        }

        // 设置总页数（用于演示）
        function setTotalPages(pages) {
            totalPages = pages;
            currentPage = 1;
            generateDemoData();
            updatePagination();
        }

        // 更新分页信息
        function updatePagination() {
            document.getElementById('pageInfo').textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
            document.getElementById('prevPage').disabled = currentPage <= 1;
            document.getElementById('nextPage').disabled = currentPage >= totalPages;
            
            // 更新跳转输入框的最大值
            const jumpInput = document.getElementById('jumpPageInput');
            jumpInput.max = totalPages;
            jumpInput.value = '';
            
            // 生成页码锚点
            generatePageNumbers(currentPage, totalPages);
        }

        // 翻页
        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                generateDemoData();
                updatePagination();
            }
        }

        // 生成页码锚点
        function generatePageNumbers(current, total) {
            const pageNumbersContainer = document.getElementById('pageNumbers');
            pageNumbersContainer.innerHTML = '';
            
            if (total <= 1) return;
            
            // 计算显示的页码范围
            let startPage = 1;
            let endPage = total;
            
            // 如果总页数超过10页，则只显示部分页码
            if (total > 10) {
                if (current <= 6) {
                    // 当前页在前面，显示 1-8 ... total
                    endPage = 8;
                } else if (current >= total - 5) {
                    // 当前页在后面，显示 1 ... (total-7)-total
                    startPage = total - 7;
                } else {
                    // 当前页在中间，显示 1 ... (current-3)-(current+3) ... total
                    startPage = current - 3;
                    endPage = current + 3;
                }
            }
            
            // 添加第一页
            if (startPage > 1) {
                addPageNumber(1, current);
                if (startPage > 2) {
                    addEllipsis();
                }
            }
            
            // 添加中间页码
            for (let i = startPage; i <= endPage; i++) {
                addPageNumber(i, current);
            }
            
            // 添加最后一页
            if (endPage < total) {
                if (endPage < total - 1) {
                    addEllipsis();
                }
                addPageNumber(total, current);
            }
        }

        // 添加页码按钮
        function addPageNumber(pageNum, currentPage) {
            const pageNumbersContainer = document.getElementById('pageNumbers');
            const pageButton = document.createElement('span');
            pageButton.className = `page-number ${pageNum === currentPage ? 'current' : ''}`;
            pageButton.textContent = pageNum;
            pageButton.onclick = () => goToPage(pageNum);
            pageNumbersContainer.appendChild(pageButton);
        }

        // 添加省略号
        function addEllipsis() {
            const pageNumbersContainer = document.getElementById('pageNumbers');
            const ellipsis = document.createElement('span');
            ellipsis.className = 'page-number ellipsis';
            ellipsis.textContent = '...';
            pageNumbersContainer.appendChild(ellipsis);
        }

        // 跳转到指定页
        function goToPage(pageNum) {
            if (pageNum >= 1 && pageNum <= totalPages && pageNum !== currentPage) {
                currentPage = pageNum;
                generateDemoData();
                updatePagination();
            }
        }

        // 页数跳转功能
        function jumpToPage() {
            const jumpInput = document.getElementById('jumpPageInput');
            const pageNum = parseInt(jumpInput.value);
            
            if (isNaN(pageNum)) {
                alert('请输入有效的页码');
                return;
            }
            
            if (pageNum < 1 || pageNum > totalPages) {
                alert(`页码必须在 1 到 ${totalPages} 之间`);
                return;
            }
            
            goToPage(pageNum);
        }
    </script>
</body>
</html>
