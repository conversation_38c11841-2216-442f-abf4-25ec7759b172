# Qwen-Solve API 管理系统

基于完整接入文档制作的两个HTML管理页面，用于查询请求日志和维护题库数据。

## 📁 文件说明

- `index.html` - 主导航页面，提供系统概览和页面导航
- `request-logs.html` - 请求日志查询页面
- `question-bank.html` - 题库维护管理页面
- `完整接入文档.md` - API接口完整文档
- `README.md` - 使用说明文档

## 🚀 快速开始

### 1. 启动API服务

确保Qwen-Solve API服务正在运行：
```bash
# 默认地址：http://localhost:8080
# 请根据实际情况调整API地址
```

### 2. 打开管理页面

直接在浏览器中打开HTML文件：

- **主页**: 打开 `index.html`
- **日志查询**: 打开 `request-logs.html`
- **题库管理**: 打开 `question-bank.html`

### 3. 配置API地址

如果API服务地址不是 `http://localhost:8080`，请修改各HTML文件中的 `API_BASE_URL` 变量：

```javascript
// 在每个HTML文件的<script>标签中找到并修改
const API_BASE_URL = 'http://your-api-server:port';
```

## 📊 请求日志查询页面功能

### 主要功能
- ✅ 实时统计信息展示（总请求数、成功率、处理时间等）
- ✅ 日志列表查看（支持分页）
- ✅ 多条件筛选（状态、时间范围）
- ✅ 日志详情查看
- ✅ 日志删除功能
- ✅ 响应式设计，支持移动端

### 使用说明
1. **查看统计**: 页面顶部显示实时统计信息
2. **筛选日志**: 使用筛选器按状态、时间范围查找日志
3. **查看详情**: 点击"查看"按钮查看日志详细信息
4. **删除日志**: 点击"删除"按钮删除不需要的日志记录

### 界面特点
- 🎨 现代化UI设计，操作直观
- 📱 响应式布局，适配各种屏幕
- ⚡ 实时数据更新
- 🔍 强大的搜索和筛选功能

## 📚 题库维护管理页面功能

### 主要功能
- ✅ 题目列表展示（支持分页）
- ✅ 多条件搜索（类型、验证状态、关键词）
- ✅ 新增题目（支持单选题、多选题、判断题）
- ✅ 编辑题目（完整的表单编辑）
- ✅ 删除题目
- ✅ 验证状态一键切换
- ✅ 题目详情查看
- ✅ 图片预览功能

### 使用说明

#### 新增题目
1. 点击"➕ 新增题目"按钮
2. 选择题目类型（单选题/多选题/判断题）
3. 填写题目内容和选项
4. 选择正确答案
5. 填写解析（可选）
6. 点击"保存"

#### 编辑题目
1. 在题目列表中点击"编辑"按钮
2. 修改题目信息
3. 点击"保存"更新

#### 验证管理
- 点击验证状态标签可快速切换验证状态
- 已验证：✅ 绿色标签
- 未验证：⚠️ 黄色标签

### 支持的题目类型

#### 单选题
- 4个选项（A、B、C、D）
- 只能选择一个正确答案

#### 多选题
- 4个选项（A、B、C、D）
- 可以选择多个正确答案

#### 判断题
- 2个选项（A: 正确，B: 错误）
- 只能选择一个答案

## 🛠️ 技术特性

### 前端技术
- **纯HTML + CSS + JavaScript** - 无需额外框架
- **现代CSS** - Grid/Flexbox布局，CSS动画
- **Fetch API** - 现代HTTP请求处理
- **响应式设计** - 适配桌面端和移动端

### 设计特点
- **用户友好** - 直观的操作界面
- **实时反馈** - 操作结果即时显示
- **错误处理** - 完善的错误提示机制
- **数据验证** - 表单数据完整性检查

### 浏览器兼容性
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 🔧 自定义配置

### 修改API地址
```javascript
// 在HTML文件中修改
const API_BASE_URL = 'http://your-server:port';
```

### 调整分页大小
```javascript
// 修改默认分页大小
let currentPageSize = 20; // 改为你需要的数量
```

### 自定义样式
可以直接修改HTML文件中的CSS样式来调整界面外观。

## 📝 注意事项

1. **CORS设置**: 确保API服务器允许跨域请求
2. **网络连接**: 确保浏览器能访问API服务器
3. **数据格式**: 严格按照API文档的数据格式进行交互
4. **浏览器缓存**: 如有问题可尝试清除浏览器缓存

## 🐛 常见问题

### Q: 页面显示"网络错误"
A: 检查API服务是否正常运行，确认API地址配置正确

### Q: 图片无法显示
A: 确认图片URL可访问，检查网络连接

### Q: 数据不更新
A: 尝试刷新页面，检查API服务状态

### Q: 移动端显示异常
A: 确保使用现代浏览器，清除缓存后重试

## 📞 技术支持

如遇到问题，请检查：
1. 浏览器控制台错误信息
2. API服务器日志
3. 网络连接状态
4. 数据格式是否正确

## 🔄 更新日志

### v1.0 (2025-06-14)
- ✅ 完成请求日志查询页面
- ✅ 完成题库维护管理页面
- ✅ 添加主导航页面
- ✅ 实现响应式设计
- ✅ 添加完整的CRUD功能

---

**开发团队**: Qwen-Solve 开发团队  
**文档版本**: v1.0  
**最后更新**: 2025-06-14
