<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片显示功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .demo-table th,
        .demo-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }

        .demo-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #555;
        }

        .demo-table tr:hover {
            background-color: #f8f9fa;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .demo-info {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            color: #0c5460;
        }

        .image-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .example-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #f9f9f9;
        }

        .example-card h3 {
            margin-top: 0;
            color: #333;
        }

        .example-preview {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 图片显示功能演示</h1>
        
        <div class="demo-info">
            <h3>功能说明：</h3>
            <ul>
                <li><strong>缩略图显示</strong>：用户图片和题干图片都显示为40x40像素的缩略图</li>
                <li><strong>点击放大</strong>：点击缩略图可以在模态框中查看大图</li>
                <li><strong>错误处理</strong>：图片加载失败时显示错误提示</li>
                <li><strong>类型区分</strong>：模态框标题会显示"用户图片"或"题干图片"</li>
                <li><strong>响应式</strong>：大图在模态框中自适应显示</li>
            </ul>
        </div>

        <div class="image-examples">
            <div class="example-card">
                <h3>有效图片示例</h3>
                <div class="example-preview">
                    <span>用户图片：</span>
                    <div id="userImageExample"></div>
                </div>
                <div class="example-preview">
                    <span>题干图片：</span>
                    <div id="questionImageExample"></div>
                </div>
            </div>
            
            <div class="example-card">
                <h3>无效图片示例</h3>
                <div class="example-preview">
                    <span>用户图片：</span>
                    <div id="userImageBrokenExample"></div>
                </div>
                <div class="example-preview">
                    <span>题干图片：</span>
                    <div id="questionImageBrokenExample"></div>
                </div>
            </div>
            
            <div class="example-card">
                <h3>空图片示例</h3>
                <div class="example-preview">
                    <span>用户图片：</span>
                    <div id="userImageEmptyExample"></div>
                </div>
                <div class="example-preview">
                    <span>题干图片：</span>
                    <div id="questionImageEmptyExample"></div>
                </div>
            </div>
        </div>

        <table class="demo-table">
            <thead>
                <tr>
                    <th style="width: 60px;">ID</th>
                    <th style="width: 80px;">类型</th>
                    <th style="width: 300px;">题目内容</th>
                    <th style="width: 100px;">用户图片</th>
                    <th style="width: 100px;">题干图片</th>
                    <th style="width: 100px;">状态</th>
                </tr>
            </thead>
            <tbody id="demoTableBody">
                <!-- 演示数据将通过JavaScript生成 -->
            </tbody>
        </table>
    </div>

    <script>
        // 演示数据
        const demoQuestions = [
            {
                id: 1,
                quest_type: '单选题',
                quest_content: '这是一道有用户图片和题干图片的题目',
                user_image: 'https://picsum.photos/200/200?random=1',
                image_url: 'https://picsum.photos/300/200?random=2'
            },
            {
                id: 2,
                quest_type: '多选题',
                quest_content: '这是一道只有用户图片的题目',
                user_image: 'https://picsum.photos/200/200?random=3',
                image_url: null
            },
            {
                id: 3,
                quest_type: '判断题',
                quest_content: '这是一道只有题干图片的题目',
                user_image: null,
                image_url: 'https://picsum.photos/300/200?random=4'
            },
            {
                id: 4,
                quest_type: '单选题',
                quest_content: '这是一道没有图片的题目',
                user_image: null,
                image_url: null
            },
            {
                id: 5,
                quest_type: '多选题',
                quest_content: '这是一道图片链接无效的题目',
                user_image: 'https://invalid-url.com/image1.jpg',
                image_url: 'https://invalid-url.com/image2.jpg'
            }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            displayExamples();
            displayQuestions();
        });

        // 显示示例
        function displayExamples() {
            // 有效图片示例
            document.getElementById('userImageExample').innerHTML = formatUserImage('https://picsum.photos/200/200?random=10');
            document.getElementById('questionImageExample').innerHTML = formatQuestionImage('https://picsum.photos/300/200?random=11');
            
            // 无效图片示例
            document.getElementById('userImageBrokenExample').innerHTML = formatUserImage('https://invalid-url.com/user.jpg');
            document.getElementById('questionImageBrokenExample').innerHTML = formatQuestionImage('https://invalid-url.com/question.jpg');
            
            // 空图片示例
            document.getElementById('userImageEmptyExample').innerHTML = formatUserImage(null);
            document.getElementById('questionImageEmptyExample').innerHTML = formatQuestionImage(null);
        }

        // 显示题目列表
        function displayQuestions() {
            const tbody = document.getElementById('demoTableBody');
            tbody.innerHTML = '';
            
            demoQuestions.forEach(question => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${question.id}</td>
                    <td>${question.quest_type}</td>
                    <td>${question.quest_content}</td>
                    <td>${formatUserImage(question.user_image)}</td>
                    <td>${formatQuestionImage(question.image_url)}</td>
                    <td>
                        <span style="color: #28a745;">✓ 正常</span>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 格式化用户图片
        function formatUserImage(imageUrl) {
            if (!imageUrl) {
                return '<span style="color: #ccc;">无</span>';
            }

            return `<img src="${imageUrl}" alt="用户图片"
                         style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px; border: 1px solid #ddd; cursor: pointer;"
                         onclick="showImageModal('${imageUrl}', '用户图片')"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                    <span style="display: none; color: #e74c3c; font-size: 12px;">🖼️ 加载失败</span>`;
        }

        // 格式化题干图片
        function formatQuestionImage(imageUrl) {
            if (!imageUrl) {
                return '<span style="color: #ccc;">无</span>';
            }

            return `<img src="${imageUrl}" alt="题干图片"
                         style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px; border: 1px solid #ddd; cursor: pointer;"
                         onclick="showImageModal('${imageUrl}', '题干图片')"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                    <span style="display: none; color: #e74c3c; font-size: 12px;">🖼️ 加载失败</span>`;
        }

        // 显示图片放大模态框
        function showImageModal(imageUrl, imageType = '图片') {
            // 创建模态框HTML
            const modalHtml = `
                <div id="imageModal" class="modal" style="display: block; z-index: 2000;">
                    <div class="modal-content" style="max-width: 80%; max-height: 80%; padding: 20px; background: white; box-shadow: 0 8px 32px rgba(0,0,0,0.3); border-radius: 12px; position: relative; margin: 5% auto;">
                        <div style="text-align: center; position: relative;">
                            <button onclick="closeImageModal()"
                                    style="position: absolute; top: -10px; right: -10px; background: #e74c3c; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; font-size: 16px; cursor: pointer; display: flex; align-items: center; justify-content: center; z-index: 2001;">
                                ×
                            </button>
                            <div style="margin-bottom: 10px; color: #333; font-weight: 600; font-size: 16px;">
                                ${imageType}
                            </div>
                            <img src="${imageUrl}" alt="${imageType}"
                                 style="max-width: 100%; max-height: 70vh; border-radius: 8px; box-shadow: 0 2px 12px rgba(0,0,0,0.2);"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                            <div style="display: none; color: #e74c3c; font-size: 16px; padding: 20px;">
                                🖼️ 图片加载失败<br>
                                <small style="color: #666;">请检查图片链接是否有效</small>
                            </div>
                            <div style="margin-top: 15px; color: #666; font-size: 14px;">
                                点击空白处或按ESC键关闭
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的图片模态框
            const existingModal = document.getElementById('imageModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新的模态框
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 点击背景关闭
            const modal = document.getElementById('imageModal');
            modal.onclick = function(event) {
                if (event.target === this) {
                    closeImageModal();
                }
            };

            // 阻止模态框内容区域的点击事件冒泡
            modal.querySelector('.modal-content').onclick = function(event) {
                event.stopPropagation();
            };
        }

        // 关闭图片模态框
        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.remove();
            }
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageModal();
            }
        });
    </script>
</body>
</html>
