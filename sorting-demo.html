<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ID排序功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        .filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: 120px;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #555;
        }

        .filter-group select,
        .filter-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
            border-color: #3498db;
        }

        .btn-secondary {
            background-color: #95a5a6;
            color: white;
            border-color: #95a5a6;
        }

        .btn:hover {
            opacity: 0.9;
        }

        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .demo-table th,
        .demo-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }

        .demo-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #555;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        /* 可排序表头样式 */
        .sortable-header {
            cursor: pointer;
            user-select: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: color 0.3s ease;
        }

        .sortable-header:hover {
            color: #3498db;
        }

        .sortable-header #sortIcon {
            font-size: 12px;
            color: #3498db;
            font-weight: bold;
        }

        .demo-table tr:hover {
            background-color: #f8f9fa;
        }

        .type-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-single {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .type-multiple {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }

        .type-judge {
            background-color: #e8f5e8;
            color: #2e7d32;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
            border-color: #28a745;
        }

        .btn-warning {
            background-color: #ffc107;
            color: #212529;
            border-color: #ffc107;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        .demo-controls {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .demo-controls button {
            margin: 0 5px;
        }

        .sort-info {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            color: #0c5460;
        }

        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔢 ID排序功能演示</h1>
        
        <div class="sort-info">
            <strong>排序说明：</strong>
            <ul style="margin: 5px 0; padding-left: 20px;">
                <li><strong>ID倒序 (新→旧)</strong>：ID大的题目排在前面，通常是最新添加的题目</li>
                <li><strong>ID正序 (旧→新)</strong>：ID小的题目排在前面，通常是最早添加的题目</li>
                <li>可以通过下拉框选择排序方式，也可以直接点击表头的ID列进行切换</li>
            </ul>
        </div>

        <!-- 筛选器 -->
        <div class="filters">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="typeFilter">题目类型</label>
                    <select id="typeFilter">
                        <option value="">全部类型</option>
                        <option value="单选题">单选题</option>
                        <option value="多选题">多选题</option>
                        <option value="判断题">判断题</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="verifiedFilter">验证状态</label>
                    <select id="verifiedFilter">
                        <option value="">全部状态</option>
                        <option value="true">已验证</option>
                        <option value="false">未验证</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="keywordFilter">关键词搜索</label>
                    <input type="text" id="keywordFilter" placeholder="搜索题目内容...">
                </div>
                <div class="filter-group">
                    <label for="sortOrder">ID排序</label>
                    <select id="sortOrder" onchange="applySorting()">
                        <option value="desc">ID倒序 (新→旧)</option>
                        <option value="asc">ID正序 (旧→新)</option>
                    </select>
                </div>
            </div>
            <div class="filter-row">
                <button class="btn btn-primary" onclick="searchQuestions()">🔍 搜索</button>
                <button class="btn btn-secondary" onclick="resetFilters()">🔄 重置</button>
            </div>
        </div>

        <div class="demo-controls">
            <h3>生成测试数据：</h3>
            <button onclick="generateData(20)">生成20条数据</button>
            <button onclick="generateData(50)">生成50条数据</button>
            <button onclick="generateData(100)">生成100条数据</button>
        </div>

        <table class="demo-table">
            <thead>
                <tr>
                    <th style="width: 60px;">
                        <span class="sortable-header" onclick="toggleSort()" title="点击切换排序">
                            ID <span id="sortIcon">↓</span>
                        </span>
                    </th>
                    <th style="width: 80px;">类型</th>
                    <th style="width: 300px;">题目内容</th>
                    <th style="width: 100px;">状态</th>
                    <th style="width: 150px;">操作</th>
                </tr>
            </thead>
            <tbody id="demoTableBody">
                <!-- 演示数据将通过JavaScript生成 -->
            </tbody>
        </table>
    </div>

    <script>
        // 演示数据
        let allQuestions = [];
        let filteredQuestions = [];
        let currentSortOrder = 'desc'; // 默认倒序

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateSortIcon();
            generateData(30); // 默认生成30条数据
        });

        // 生成演示数据
        function generateData(count) {
            allQuestions = [];
            const types = ['单选题', '多选题', '判断题'];
            
            for (let i = 1; i <= count; i++) {
                const isVerified = Math.random() > 0.5;
                const type = types[Math.floor(Math.random() * types.length)];
                
                allQuestions.push({
                    id: i,
                    quest_type: type,
                    quest_content: `这是第${i}道题目的内容，用于演示排序功能...`,
                    is_verified: isVerified
                });
            }
            
            applyFiltersAndSort();
        }

        // 应用筛选和排序
        function applyFiltersAndSort() {
            // 应用筛选
            const typeFilter = document.getElementById('typeFilter').value;
            const verifiedFilter = document.getElementById('verifiedFilter').value;
            const keyword = document.getElementById('keywordFilter').value.trim().toLowerCase();
            
            filteredQuestions = allQuestions.filter(question => {
                if (typeFilter && question.quest_type !== typeFilter) return false;
                if (verifiedFilter !== '' && question.is_verified !== (verifiedFilter === 'true')) return false;
                if (keyword && !question.quest_content.toLowerCase().includes(keyword)) return false;
                return true;
            });
            
            // 应用排序
            filteredQuestions.sort((a, b) => {
                if (currentSortOrder === 'desc') {
                    return b.id - a.id; // 倒序：大ID在前
                } else {
                    return a.id - b.id; // 正序：小ID在前
                }
            });
            
            displayQuestions();
        }

        // 显示题目列表
        function displayQuestions() {
            const tbody = document.getElementById('demoTableBody');
            tbody.innerHTML = '';
            
            if (filteredQuestions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 40px; color: #7f8c8d;">📭 暂无符合条件的题目数据</td></tr>';
                return;
            }
            
            filteredQuestions.forEach(question => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><strong>${question.id}</strong></td>
                    <td><span class="type-badge type-${getTypeClass(question.quest_type)}">${question.quest_type}</span></td>
                    <td>${question.quest_content}</td>
                    <td>
                        <button class="btn ${question.is_verified ? 'btn-success' : 'btn-danger'}" 
                                onclick="toggleDemo(this, ${question.id})">
                            ${question.is_verified ? '已验证' : '未验证'}
                        </button>
                    </td>
                    <td>
                        <button class="btn btn-warning" onclick="editDemo(${question.id})">编辑</button>
                        <button class="btn btn-danger" onclick="deleteDemo(${question.id})">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 搜索题目
        function searchQuestions() {
            applyFiltersAndSort();
        }

        // 重置筛选器
        function resetFilters() {
            document.getElementById('typeFilter').value = '';
            document.getElementById('verifiedFilter').value = '';
            document.getElementById('keywordFilter').value = '';
            document.getElementById('sortOrder').value = 'desc';
            currentSortOrder = 'desc';
            updateSortIcon();
            applyFiltersAndSort();
        }

        // 应用排序
        function applySorting() {
            currentSortOrder = document.getElementById('sortOrder').value;
            updateSortIcon();
            applyFiltersAndSort();
        }

        // 切换排序
        function toggleSort() {
            currentSortOrder = currentSortOrder === 'desc' ? 'asc' : 'desc';
            document.getElementById('sortOrder').value = currentSortOrder;
            updateSortIcon();
            applyFiltersAndSort();
        }

        // 更新排序图标
        function updateSortIcon() {
            const sortIcon = document.getElementById('sortIcon');
            if (currentSortOrder === 'desc') {
                sortIcon.textContent = '↓';
                sortIcon.title = '当前倒序 (新→旧)，点击切换为正序';
            } else {
                sortIcon.textContent = '↑';
                sortIcon.title = '当前正序 (旧→新)，点击切换为倒序';
            }
        }

        // 工具函数
        function getTypeClass(questType) {
            switch (questType) {
                case '单选题': return 'single';
                case '多选题': return 'multiple';
                case '判断题': return 'judge';
                default: return 'single';
            }
        }

        // 演示功能
        function toggleDemo(button, id) {
            const question = allQuestions.find(q => q.id === id);
            if (question) {
                question.is_verified = !question.is_verified;
                button.className = `btn ${question.is_verified ? 'btn-success' : 'btn-danger'}`;
                button.textContent = question.is_verified ? '已验证' : '未验证';
            }
        }

        function editDemo(id) {
            alert(`编辑题目 ${id} - 在实际应用中，这里会打开编辑模态框`);
        }

        function deleteDemo(id) {
            if (confirm(`确定要删除题目 ${id} 吗？`)) {
                allQuestions = allQuestions.filter(q => q.id !== id);
                applyFiltersAndSort();
                alert(`题目 ${id} 已删除`);
            }
        }
    </script>
</body>
</html>
