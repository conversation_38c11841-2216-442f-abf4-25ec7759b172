<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑刷新功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        .demo-info {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            color: #0c5460;
        }

        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .demo-table th,
        .demo-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }

        .demo-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #555;
        }

        .demo-table tr:hover {
            background-color: #f8f9fa;
        }

        .btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            margin-right: 5px;
            margin-bottom: 5px;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            opacity: 0.9;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
            border-color: #28a745;
        }

        .btn-warning {
            background-color: #ffc107;
            color: #212529;
            border-color: #ffc107;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }

        .type-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-single {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .type-multiple {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }

        .type-judge {
            background-color: #e8f5e8;
            color: #2e7d32;
        }

        .demo-controls {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .demo-controls button {
            margin: 0 5px;
        }

        .update-animation {
            background-color: #e8f5e8 !important;
            transition: background-color 0.3s ease;
        }

        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            display: none;
        }

        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-success {
            color: #28a745;
        }

        .log-info {
            color: #17a2b8;
        }

        .log-warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 编辑刷新功能演示</h1>
        
        <div class="demo-info">
            <h3>功能说明：</h3>
            <ul>
                <li><strong>智能刷新</strong>：编辑题目后，只刷新对应的行数据，不重新加载整个列表</li>
                <li><strong>数据同步</strong>：从API重新获取最新数据，确保显示的是服务器端的最新状态</li>
                <li><strong>验证状态</strong>：切换验证状态后也会刷新该行数据</li>
                <li><strong>视觉反馈</strong>：更新后的行会有绿色背景动画提示</li>
                <li><strong>错误处理</strong>：如果单行刷新失败，会自动回退到重新加载整个列表</li>
            </ul>
        </div>

        <div class="success-message" id="successMessage"></div>

        <div class="demo-controls">
            <h3>模拟操作：</h3>
            <button class="btn btn-primary" onclick="simulateEdit(1)">编辑题目1</button>
            <button class="btn btn-primary" onclick="simulateEdit(2)">编辑题目2</button>
            <button class="btn btn-warning" onclick="simulateToggleVerification(1)">切换题目1验证状态</button>
            <button class="btn btn-warning" onclick="simulateToggleVerification(3)">切换题目3验证状态</button>
            <button class="btn btn-danger" onclick="clearLog()">清空日志</button>
        </div>

        <table class="demo-table">
            <thead>
                <tr>
                    <th style="width: 60px;">ID</th>
                    <th style="width: 80px;">类型</th>
                    <th style="width: 300px;">题目内容</th>
                    <th style="width: 200px;">答案</th>
                    <th style="width: 100px;">状态</th>
                    <th style="width: 150px;">操作</th>
                </tr>
            </thead>
            <tbody id="demoTableBody">
                <!-- 演示数据将通过JavaScript生成 -->
            </tbody>
        </table>

        <div class="log-area">
            <div><strong>操作日志：</strong></div>
            <div id="logContent">
                <div class="log-entry log-info">系统已初始化，准备演示编辑刷新功能...</div>
            </div>
        </div>
    </div>

    <script>
        // 演示数据
        let demoQuestions = [
            {
                id: 1,
                quest_type: '单选题',
                quest_content: '这是第1道题目的内容',
                answer: { A: '选项A内容' },
                is_verified: true,
                lastUpdated: new Date().toLocaleTimeString()
            },
            {
                id: 2,
                quest_type: '多选题',
                quest_content: '这是第2道题目的内容',
                answer: { A: '选项A内容', B: '选项B内容' },
                is_verified: false,
                lastUpdated: new Date().toLocaleTimeString()
            },
            {
                id: 3,
                quest_type: '判断题',
                quest_content: '这是第3道题目的内容',
                answer: { A: '正确' },
                is_verified: true,
                lastUpdated: new Date().toLocaleTimeString()
            }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            displayQuestions();
        });

        // 显示题目列表
        function displayQuestions() {
            const tbody = document.getElementById('demoTableBody');
            tbody.innerHTML = '';
            
            demoQuestions.forEach(question => {
                const row = document.createElement('tr');
                row.id = `question-row-${question.id}`;
                row.innerHTML = `
                    <td>${question.id}</td>
                    <td><span class="type-badge type-${getTypeClass(question.quest_type)}">${question.quest_type}</span></td>
                    <td>
                        <div title="${question.quest_content}">
                            ${question.quest_content}
                        </div>
                        <small style="color: #666;">更新时间: ${question.lastUpdated}</small>
                    </td>
                    <td>
                        <div>
                            ${formatAnswer(question.answer)}
                        </div>
                    </td>
                    <td>
                        <button class="btn ${question.is_verified ? 'btn-success' : 'btn-danger'}"
                                onclick="simulateToggleVerification(${question.id})">
                            ${question.is_verified ? '已验证' : '未验证'}
                        </button>
                    </td>
                    <td>
                        <button class="btn btn-warning" onclick="simulateEdit(${question.id})">编辑</button>
                        <button class="btn btn-danger" onclick="simulateDelete(${question.id})">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 模拟编辑题目
        function simulateEdit(questionId) {
            addLog(`开始编辑题目 ${questionId}...`, 'info');
            
            // 模拟编辑操作
            setTimeout(() => {
                const question = demoQuestions.find(q => q.id === questionId);
                if (question) {
                    // 模拟数据更改
                    question.quest_content = `${question.quest_content} (已编辑)`;
                    question.lastUpdated = new Date().toLocaleTimeString();
                    
                    addLog(`题目 ${questionId} 编辑成功，正在刷新数据...`, 'success');
                    
                    // 模拟从API刷新数据
                    refreshQuestionRow(questionId);
                }
            }, 500);
        }

        // 模拟切换验证状态
        function simulateToggleVerification(questionId) {
            const question = demoQuestions.find(q => q.id === questionId);
            if (question) {
                const newStatus = !question.is_verified;
                addLog(`切换题目 ${questionId} 验证状态为: ${newStatus ? '已验证' : '未验证'}`, 'info');
                
                setTimeout(() => {
                    question.is_verified = newStatus;
                    question.lastUpdated = new Date().toLocaleTimeString();
                    
                    addLog(`验证状态更新成功，正在刷新数据...`, 'success');
                    refreshQuestionRow(questionId);
                }, 300);
            }
        }

        // 模拟删除题目
        function simulateDelete(questionId) {
            if (confirm(`确定要删除题目 ${questionId} 吗？`)) {
                addLog(`删除题目 ${questionId}...`, 'warning');
                demoQuestions = demoQuestions.filter(q => q.id !== questionId);
                displayQuestions();
                addLog(`题目 ${questionId} 已删除`, 'success');
            }
        }

        // 刷新单行数据（模拟API调用）
        function refreshQuestionRow(questionId) {
            addLog(`从API获取题目 ${questionId} 的最新数据...`, 'info');
            
            // 模拟API延迟
            setTimeout(() => {
                const question = demoQuestions.find(q => q.id === questionId);
                if (question) {
                    updateQuestionRow(questionId, question);
                    addLog(`题目 ${questionId} 数据刷新完成`, 'success');
                    showSuccess(`题目 ${questionId} 已更新`);
                } else {
                    addLog(`题目 ${questionId} 不存在，重新加载整个列表`, 'warning');
                    displayQuestions();
                }
            }, 200);
        }

        // 更新单行数据
        function updateQuestionRow(questionId, questionData) {
            const row = document.getElementById(`question-row-${questionId}`);
            if (row) {
                // 重新生成行内容
                row.innerHTML = `
                    <td>${questionData.id}</td>
                    <td><span class="type-badge type-${getTypeClass(questionData.quest_type)}">${questionData.quest_type}</span></td>
                    <td>
                        <div title="${questionData.quest_content}">
                            ${questionData.quest_content}
                        </div>
                        <small style="color: #666;">更新时间: ${questionData.lastUpdated}</small>
                    </td>
                    <td>
                        <div>
                            ${formatAnswer(questionData.answer)}
                        </div>
                    </td>
                    <td>
                        <button class="btn ${questionData.is_verified ? 'btn-success' : 'btn-danger'}"
                                onclick="simulateToggleVerification(${questionData.id})">
                            ${questionData.is_verified ? '已验证' : '未验证'}
                        </button>
                    </td>
                    <td>
                        <button class="btn btn-warning" onclick="simulateEdit(${questionData.id})">编辑</button>
                        <button class="btn btn-danger" onclick="simulateDelete(${questionData.id})">删除</button>
                    </td>
                `;
                
                // 添加更新动画
                row.classList.add('update-animation');
                setTimeout(() => {
                    row.classList.remove('update-animation');
                }, 2000);
            }
        }

        // 工具函数
        function getTypeClass(questType) {
            switch (questType) {
                case '单选题': return 'single';
                case '多选题': return 'multiple';
                case '判断题': return 'judge';
                default: return 'single';
            }
        }

        function formatAnswer(answer) {
            if (!answer || typeof answer !== 'object') return '无';
            return Object.keys(answer).map(key => `${key}: ${answer[key]}`).join('<br>');
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 3000);
        }

        function addLog(message, type = 'info') {
            const logContent = document.getElementById('logContent');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContent.appendChild(logEntry);
            
            // 自动滚动到底部
            const logArea = logContent.parentElement;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            const logContent = document.getElementById('logContent');
            logContent.innerHTML = '<div class="log-entry log-info">日志已清空</div>';
        }
    </script>
</body>
</html>
